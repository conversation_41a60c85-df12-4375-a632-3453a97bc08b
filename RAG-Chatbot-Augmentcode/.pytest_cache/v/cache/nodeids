["chatbot-engine/tests/test_phase5_demo.py::TestPhase5Infrastructure::test_api_endpoint", "chatbot-engine/tests/test_phase5_demo.py::TestPhase5Infrastructure::test_async_functionality", "chatbot-engine/tests/test_phase5_demo.py::TestPhase5Infrastructure::test_basic_functionality", "chatbot-engine/tests/test_phase5_demo.py::TestPhase5Infrastructure::test_data_sources_fixture", "chatbot-engine/tests/test_phase5_demo.py::TestPhase5Infrastructure::test_exception_handling", "chatbot-engine/tests/test_phase5_demo.py::TestPhase5Infrastructure::test_fixture_usage", "chatbot-engine/tests/test_phase5_demo.py::TestPhase5Infrastructure::test_mock_document_chunks", "chatbot-engine/tests/test_phase5_demo.py::TestPhase5Infrastructure::test_mock_functionality", "chatbot-engine/tests/test_phase5_demo.py::TestPhase5Infrastructure::test_parametrized_testing[hello-HELLO]", "chatbot-engine/tests/test_phase5_demo.py::TestPhase5Infrastructure::test_parametrized_testing[test-TEST]", "chatbot-engine/tests/test_phase5_demo.py::TestPhase5Infrastructure::test_parametrized_testing[world-WORLD]", "chatbot-engine/tests/test_phase5_demo.py::TestPhase5Infrastructure::test_performance_measurement", "chatbot-engine/tests/test_phase5_demo.py::TestPhase5Integration::test_component_integration", "chatbot-engine/tests/test_phase5_demo.py::TestPhase5Performance::test_concurrent_operations", "chatbot-engine/tests/test_phase5_demo.py::TestPhase5Performance::test_response_time_measurement", "chatbot-engine/tests/test_phase5_demo.py::TestPhase5Security::test_authentication_simulation", "chatbot-engine/tests/test_phase5_demo.py::TestPhase5Security::test_input_validation", "chatbot-engine/tests/test_phase5_demo.py::test_phase5_completion", "chatbot-engine/tests/unit/test_api.py::test_admin_add_source", "chatbot-engine/tests/unit/test_api.py::test_admin_list_sources", "chatbot-engine/tests/unit/test_api.py::test_admin_metrics", "chatbot-engine/tests/unit/test_api.py::test_chat_history", "chatbot-engine/tests/unit/test_api.py::test_chat_query", "chatbot-engine/tests/unit/test_api.py::test_chat_stream", "chatbot-engine/tests/unit/test_api.py::test_clear_chat_session", "chatbot-engine/tests/unit/test_api.py::test_detailed_health_check", "chatbot-engine/tests/unit/test_api.py::test_health_check", "chatbot-engine/tests/unit/test_api.py::test_liveness_check", "chatbot-engine/tests/unit/test_api.py::test_readiness_check", "chatbot-engine/tests/unit/test_api.py::test_root_endpoint"]